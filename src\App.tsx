import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Files,
  Search,
  GitBranch,
  Settings,
  Bot,
  Terminal as TerminalIcon
} from 'lucide-react';

import CodeArea from "./components/CodeArea"
import Sidebar from "./components/Sidebar"
import Titlebar from "./components/Titlebar"
import AgentConsole from "./components/AgentConsole"
import Terminal from "./components/Terminal"
import { SourceProvider } from "./context/SourceContext"
import { useUIStore } from '@/stores/uiStore';
import { useAgentStore } from '@/stores/agentStore';
import { AgentList } from './components/agents';

export default function App() {
  const [activeView, setActiveView] = useState('explorer');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [panelOpen, setPanelOpen] = useState(true);
  // Use Zustand UI store for right panel state if available
  // const rightPanelOpen = useUIStore((s) => s.panels.agentPanelOpen);
  const [rightPanelOpen, setRightPanelOpen] = useState(true);
  const agents = useAgentStore((s) => s.agents);

  const handleViewChange = useCallback((view: string) => {
    if (view === activeView && sidebarOpen) {
      setSidebarOpen(false);
    } else {
      setActiveView(view);
      setSidebarOpen(true);
    }
  }, [activeView, sidebarOpen]);

  // Activity Bar Component
  const ActivityBar = () => {
    const activities = [
      { id: 'explorer', icon: Files, label: 'Explorer' },
      { id: 'search', icon: Search, label: 'Search' },
      { id: 'git', icon: GitBranch, label: 'Source Control' },
      { id: 'ai', icon: Bot, label: 'AI Assistant' },
      { id: 'terminal', icon: TerminalIcon, label: 'Terminal' },
    ];

    const bottomActivities = [
      { id: 'settings', icon: Settings, label: 'Settings' },
    ];

    return (
      <div className="activity-bar w-12 h-full glass-panel flex flex-col py-2">
        <TooltipProvider>
          <div className="flex-1 flex flex-col space-y-1">
            {activities.map((activity) => {
              const Icon = activity.icon;
              return (
                <Tooltip key={activity.id}>
                  <TooltipTrigger asChild>
                    <button
                      className={`w-10 h-10 mx-1 rounded-md flex items-center justify-center transition-all duration-200 ${
                        activeView === activity.id
                          ? 'bg-neon-purple/30 text-neon-purple shadow-lg shadow-neon-purple/20 border-l-2 border-neon-purple'
                          : 'text-gray-400 hover:text-neon-purple hover:bg-neon-purple/10 hover:shadow-md hover:shadow-neon-purple/10'
                      }`}
                      onClick={() => handleViewChange(activity.id)}
                    >
                      <Icon size={18} />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent side="right" className="bg-surface-secondary border-surface-tertiary text-gray-200">
                    <p>{activity.label}</p>
                  </TooltipContent>
                </Tooltip>
              );
            })}
          </div>

          <div className="flex flex-col space-y-1">
            {bottomActivities.map((activity) => {
              const Icon = activity.icon;
              return (
                <Tooltip key={activity.id}>
                  <TooltipTrigger asChild>
                    <button
                      className={`w-10 h-10 mx-1 rounded-md flex items-center justify-center transition-all duration-200 ${
                        activeView === activity.id
                          ? 'bg-neon-purple/30 text-neon-purple shadow-lg shadow-neon-purple/20 border-l-2 border-neon-purple'
                          : 'text-gray-400 hover:text-neon-purple hover:bg-neon-purple/10 hover:shadow-md hover:shadow-neon-purple/10'
                      }`}
                      onClick={() => handleViewChange(activity.id)}
                    >
                      <Icon size={18} />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent side="right" className="bg-surface-secondary border-surface-tertiary text-gray-200">
                    <p>{activity.label}</p>
                  </TooltipContent>
                </Tooltip>
              );
            })}
          </div>
        </TooltipProvider>
      </div>
    );
  };

  // Render sidebar content based on active view
  const renderSidebarContent = () => {
    switch (activeView) {
      case 'explorer':
        return <Sidebar />;
      case 'ai':
        return (
          <div className="flex flex-col h-full">
            <AgentList />
            <div className="flex-1 min-h-0">
              {agents.length > 0 ? (
                <AgentConsole agent={agents[0]} />
              ) : (
                <div className="p-4 text-gray-400">No agent available.</div>
              )}
            </div>
          </div>
        );
      default:
        return <Sidebar />;
    }
  };

  // Keyboard shortcuts
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'b':
            e.preventDefault();
            setSidebarOpen(!sidebarOpen);
            break;
          case '`':
            e.preventDefault();
            setPanelOpen(!panelOpen);
            break;
          case 'j':
            e.preventDefault();
            setPanelOpen(!panelOpen);
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [sidebarOpen, panelOpen]);

  return (
    <div className="wrapper">
      <Titlebar />
      <div id="editor" className="h-screen pt-7 grid grid-cols-[48px_auto_1fr_auto] grid-rows-[1fr_auto] overflow-hidden bg-[var(--background)]">
        {/* Activity Bar */}
        <div className="row-span-2">
          <ActivityBar />
        </div>

        {/* Left Sidebar */}
        {sidebarOpen && (
          <div className="row-span-2 glass-panel w-80 min-w-72 max-w-96">
            {renderSidebarContent()}
          </div>
        )}

        {/* Main Editor Area */}
        <div className={`${sidebarOpen ? 'col-start-3' : 'col-start-2'} row-start-1`}>
          <SourceProvider>
            <CodeArea />
          </SourceProvider>
        </div>

        {/* Right Panel (Always-On Agent Console) */}
        {rightPanelOpen && (
          <div className="row-span-2 glass-panel w-80 min-w-72 max-w-96 flex flex-col">
            <AgentList />
            <div className="flex-1 min-h-0">
              {agents.length > 0 ? (
                <AgentConsole agent={agents[0]} />
              ) : (
                <div className="p-4 text-gray-400">No agent available.</div>
              )}
            </div>
          </div>
        )}

        {/* Bottom Terminal Panel */}
        {panelOpen && (
          <div className={`${sidebarOpen ? 'col-start-3' : 'col-start-2'} ${rightPanelOpen ? 'col-end-4' : 'col-end-5'} row-start-2 glass-panel h-60 min-h-48 max-h-80`}>
            <Terminal />
          </div>
        )}
      </div>
    </div>
  );
}


