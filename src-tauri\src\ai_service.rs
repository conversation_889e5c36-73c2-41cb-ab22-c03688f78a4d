// AI Service integration for OpenAI/Anthropic APIs
// TODO: Add dependencies: reqwest, serde_json, etc.

use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::error::Error;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ChatMessage {
    pub role: String,
    pub content: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AIResponse {
    pub content: String,
    pub provider: String,
    pub tokens_used: Option<u32>,
}

pub struct AIService {
    client: Client,
    openai_key: Option<String>,
    anthropic_key: Option<String>,
}

impl AIService {
    pub fn new() -> Self {
        Self {
            client: Client::new(),
            openai_key: std::env::var("OPENAI_API_KEY").ok(),
            anthropic_key: std::env::var("ANTHROPIC_API_KEY").ok(),
        }
    }

    pub async fn chat_completion(&self, messages: Vec<ChatMessage>, provider: &str) -> Result<AIResponse, Box<dyn Error>> {
        match provider {
            "openai" => self.openai_chat(messages).await,
            "anthropic" => self.anthropic_chat(messages).await,
            _ => Err("Unsupported provider".into()),
        }
    }

    async fn openai_chat(&self, messages: Vec<ChatMessage>) -> Result<AIResponse, Box<dyn Error>> {
        let api_key = self.openai_key.as_ref().ok_or("Missing OpenAI API key")?;
        let url = "https://api.openai.com/v1/chat/completions";
        let req_body = serde_json::json!({
            "model": "gpt-3.5-turbo",
            "messages": messages.iter().map(|m| serde_json::json!({"role": m.role, "content": m.content})).collect::<Vec<_>>(),
        });

        let resp = self.client
            .post(url)
            .bearer_auth(api_key)
            .json(&req_body)
            .send()
            .await?
            .error_for_status()?;

        let resp_json: serde_json::Value = resp.json().await?;
        let content = resp_json["choices"][0]["message"]["content"].as_str().unwrap_or("").to_string();
        let tokens_used = resp_json["usage"]["total_tokens"].as_u64().map(|v| v as u32);

        Ok(AIResponse {
            content,
            provider: "openai".to_string(),
            tokens_used,
        })
    }

    async fn anthropic_chat(&self, _messages: Vec<ChatMessage>) -> Result<AIResponse, Box<dyn Error>> {
        // TODO: Implement Anthropic API call if required by plan
        Err("Anthropic not implemented").into()
    }
}
