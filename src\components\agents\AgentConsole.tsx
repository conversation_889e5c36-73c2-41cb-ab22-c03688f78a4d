import { useAgentStore, Agent } from '@/stores/agentStore';
import { Conversation } from '@/components/ui/kibo-ui/ai/conversation';
import { Message } from '@/components/ui/kibo-ui/ai/message';
import { Reasoning } from '@/components/ui/kibo-ui/ai/reasoning';
import { Response } from '@/components/ui/kibo-ui/ai/response';
import { ScrollArea } from '@/components/ui/scroll-area';

export function AgentConsole({ agent }: { agent: Agent }) {
  // In a real app, you would fetch the agent's thought stream and responses from state or backend
  // Here, we use mock data for demonstration
  const thoughts = [
    { role: 'system', content: 'Agent initialized.' },
    { role: 'user', content: 'Analyze this file.' },
    { role: 'agent', content: 'Analyzing file structure...' },
    { role: 'agent', content: 'Found 3 issues.' },
  ];
  const reasoning = 'The agent is using static analysis to find code issues.';
  const response = '3 issues found. Would you like to see details?';

  return (
    <ScrollArea className="h-80 w-full p-2">
      <Conversation>
        {thoughts.map((msg, i) => (
          <Message key={i} role={msg.role} content={msg.content} />
        ))}
        <Reasoning>{reasoning}</Reasoning>
        <Response>{response}</Response>
      </Conversation>
    </ScrollArea>
  );
} 