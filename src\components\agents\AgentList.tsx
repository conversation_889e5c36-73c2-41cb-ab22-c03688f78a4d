import { useAgentStore, Agent } from '@/stores/agentStore';
import { AgentStatus } from './AgentStatus';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card } from '@/components/ui/kibo-ui/code-block';

export function AgentList({ onSelect }: { onSelect?: (agent: Agent) => void }) {
  const agents = useAgentStore((s) => s.agents);

  return (
    <ScrollArea className="h-64 w-full">
      <div className="space-y-2">
        {agents.map((agent) => (
          <Card
            key={agent.id}
            className="flex items-center justify-between p-2 cursor-pointer hover:bg-muted"
            onClick={() => onSelect?.(agent)}
          >
            <div>
              <div className="font-semibold">{agent.agent_type}</div>
              <div className="text-xs text-muted-foreground">{agent.id.slice(0, 8)}</div>
            </div>
            <AgentStatus status={agent.status} />
          </Card>
        ))}
      </div>
    </ScrollArea>
  );
} 