import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { invoke } from '@tauri-apps/api/tauri';

export type AgentType = 'Code' | 'Debug' | 'Optimize' | 'Analyze';
export type AgentStatus = 'Idle' | 'Thinking' | 'Working' | 'Error';

export interface Agent {
  id: string;
  agent_type: AgentType;
  status: AgentStatus;
  capabilities: string[];
  current_task?: string;
  created_at: string;
}

interface AgentStore {
  agents: Agent[];
  createAgent: (type: AgentType) => Promise<string>;
  listAgents: () => Promise<void>;
  getAgent: (id: string) => Promise<Agent | undefined>;
  updateAgentStatus: (id: string, status: AgentStatus) => Promise<void>;
  sendAIMessage: (messages: any[], provider: string) => Promise<any>;
}

export const useAgentStore = create<AgentStore>()(
  persist(
    (set, get) => ({
      agents: [],

      createAgent: async (type: AgentType) => {
        const agentId = await invoke<string>('create_agent', { agentType: type });
        await get().listAgents();
        return agentId;
      },

      listAgents: async () => {
        const agents = await invoke<Agent[]>('list_agents');
        set({ agents });
      },

      getAgent: async (id: string) => {
        try {
          const agent = await invoke<Agent>('get_agent', { agentId: id });
          return agent;
        } catch {
          return undefined;
        }
      },

      updateAgentStatus: async (id: string, status: AgentStatus) => {
        await invoke('update_agent_status', { agentId: id, status });
        await get().listAgents();
      },

      sendAIMessage: async (messages: any[], provider: string) => {
        const response = await invoke('ai_chat', { messages, provider });
        return response;
      }
    }),
    { name: 'agent-store' }
  )
);
