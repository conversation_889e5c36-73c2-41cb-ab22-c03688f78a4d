import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type Theme = 'light' | 'dark' | 'system';

interface PanelState {
  sidebarOpen: boolean;
  terminalOpen: boolean;
  agentPanelOpen: boolean;
}

interface UIStore {
  layout: string;
  panels: PanelState;
  theme: Theme;
  preferences: Record<string, any>;

  setLayout: (layout: string) => void;
  togglePanel: (panel: keyof PanelState) => void;
  setTheme: (theme: Theme) => void;
  setPreference: (key: string, value: any) => void;
}

export const useUIStore = create<UIStore>()(
  persist(
    (set, get) => ({
      layout: 'default',
      panels: {
        sidebarOpen: true,
        terminalOpen: false,
        agentPanelOpen: false,
      },
      theme: 'system',
      preferences: {},

      setLayout: (layout) => set({ layout }),
      togglePanel: (panel) => set((state) => ({
        panels: { ...state.panels, [panel]: !state.panels[panel] }
      })),
      setTheme: (theme) => set({ theme }),
      setPreference: (key, value) => set((state) => ({
        preferences: { ...state.preferences, [key]: value }
      })),
    }),
    { name: 'ui-store' }
  )
);
