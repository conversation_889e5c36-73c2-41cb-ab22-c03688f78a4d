use rusqlite::{Connection, Result};
use tauri::api::path::data_dir;
use tauri::Config;
use walkdir::WalkDir;
use rusqlite::params;

pub struct KnowledgeBase {
    conn: Connection,
}

impl KnowledgeBase {
    pub fn new(config: &Config) -> Result<Self> {
        let app_data = data_dir().unwrap();
        let db_path = app_data.join("knowledge.db");
        let conn = Connection::open(db_path)?;
        conn.execute(
            "CREATE TABLE IF NOT EXISTS snippets (
                id INTEGER PRIMARY KEY,
                code TEXT NOT NULL,
                language TEXT NOT NULL,
                description TEXT,
                tags TEXT
            )",
            (),
        )?;
        // New tables for project context, agent sessions, and code patterns
        conn.execute(
            "CREATE TABLE IF NOT EXISTS project_context (
                id INTEGER PRIMARY KEY,
                file_path TEXT NOT NULL,
                content_hash TEXT NOT NULL,
                ast_data TEXT,
                dependencies TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )",
            (),
        )?;
        conn.execute(
            "CREATE TABLE IF NOT EXISTS agent_sessions (
                id INTEGER PRIMARY KEY,
                agent_id TEXT NOT NULL,
                session_data TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )",
            (),
        )?;
        conn.execute(
            "CREATE TABLE IF NOT EXISTS code_patterns (
                id INTEGER PRIMARY KEY,
                pattern_type TEXT NOT NULL,
                pattern_data TEXT NOT NULL,
                frequency INTEGER DEFAULT 1,
                last_seen DATETIME DEFAULT CURRENT_TIMESTAMP
            )",
            (),
        )?;
        Ok(Self { conn })
    }

    pub fn insert_snippet(&self, code: &str, language: &str, description: &str, tags: &str) -> Result<()> {
        self.conn.execute(
            "INSERT INTO snippets (code, language, description, tags) VALUES (?1, ?2, ?3, ?4)",
            (code, language, description, tags),
        )?;
        Ok(())
    }

    pub fn query_snippets(&self, query: &str) -> Result<Vec<(String, String, String, String)>> {
        let mut stmt = self.conn.prepare("SELECT code, language, description, tags FROM snippets WHERE description LIKE ?1 OR tags LIKE ?1")?;
        let rows = stmt.query_map([format!("%{}%", query)], |row| {
            Ok((row.get(0)?, row.get(1)?, row.get(2)?, row.get(3)?))
        })?;
        rows.collect()
    }

    pub fn delete_snippet(&self, id: i64) -> Result<()> {
        self.conn.execute("DELETE FROM snippets WHERE id = ?1", [id])?;
        Ok(())
    }

    // New methods for project context tracking and agent sessions
    pub fn index_project(&self, project_path: &str) -> Result<()> {
        // Walk the directory and index files
        for entry in WalkDir::new(project_path).into_iter().filter_map(|e| e.ok()) {
            let path = entry.path();
            if path.is_file() {
                let file_path = path.to_string_lossy();
                let content = std::fs::read_to_string(path).unwrap_or_default();
                let content_hash = format!("{:x}", md5::compute(&content));
                // For demo, ast_data and dependencies are left empty
                self.conn.execute(
                    "INSERT OR IGNORE INTO project_context (file_path, content_hash, ast_data, dependencies) VALUES (?1, ?2, '', '')",
                    params![file_path, content_hash],
                )?;
            }
        }
        Ok(())
    }

    pub fn store_context(&self, file_path: &str, context: &str) -> Result<()> {
        // Store file context and relationships
        self.conn.execute(
            "INSERT OR REPLACE INTO project_context (file_path, content_hash, ast_data, dependencies) VALUES (?1, '', ?2, '')",
            params![file_path, context],
        )?;
        Ok(())
    }

    pub fn query_context(&self, query: &str) -> Result<Vec<String>> {
        let mut stmt = self.conn.prepare("SELECT file_path FROM project_context WHERE file_path LIKE ?1 OR ast_data LIKE ?1")?;
        let rows = stmt.query_map([format!("%{}%", query)], |row| {
            Ok(row.get(0)?)
        })?;
        rows.collect()
    }

    pub fn store_agent_session(&self, agent_id: &str, session_data: &str) -> Result<()> {
        self.conn.execute(
            "INSERT INTO agent_sessions (agent_id, session_data) VALUES (?1, ?2)",
            params![agent_id, session_data],
        )?;
        Ok(())
    }
}