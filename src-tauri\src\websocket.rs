// WebSocket server for real-time agent communication
// TODO: Add dependencies: tokio-tungstenite, futures-util, etc.

use tokio_tungstenite::{accept_async, tungstenite::Message};
use tokio::net::TcpListener;
use futures_util::{StreamExt, SinkExt};
use std::sync::{Arc, Mutex};
use std::collections::HashSet;
use tokio::sync::broadcast;

pub struct WebSocketServer {
    pub addr: String,
    pub broadcaster: broadcast::Sender<String>,
}

impl WebSocketServer {
    pub fn new(addr: &str) -> Self {
        let (tx, _rx) = broadcast::channel(100);
        Self {
            addr: addr.to_string(),
            broadcaster: tx,
        }
    }

    pub async fn start(&self) -> Result<(), Box<dyn std::error::Error>> {
        let listener = TcpListener::bind(&self.addr).await?;
        println!("WebSocket server listening on {}", &self.addr);
        let tx = self.broadcaster.clone();
        loop {
            let (stream, _addr) = listener.accept().await?;
            let mut rx = tx.subscribe();
            let tx_inner = tx.clone();
            tokio::spawn(async move {
                let ws_stream = accept_async(stream).await;
                match ws_stream {
                    Ok(mut ws) => {
                        // Spawn a task to forward broadcasts to this client
                        let mut ws_sink = ws.split().1;
                        let mut rx_clone = rx.resubscribe();
                        tokio::spawn(async move {
                            while let Ok(msg) = rx_clone.recv().await {
                                let _ = ws_sink.send(Message::Text(msg)).await;
                            }
                        });
                        // Handle incoming messages (optional)
                        while let Some(msg) = ws.next().await {
                            match msg {
                                Ok(Message::Text(text)) => {
                                    println!("Received: {}", text);
                                    // Optionally handle commands from client
                                }
                                Ok(Message::Binary(_)) => {}
                                Ok(Message::Close(_)) => break,
                                _ => {}
                            }
                        }
                    }
                    Err(e) => eprintln!("WebSocket error: {}", e),
                }
            });
        }
    }

    pub fn broadcast_agent_update(&self, update: String) -> Result<(), Box<dyn std::error::Error>> {
        self.broadcaster.send(update)?;
        Ok(())
    }
}
