{"build": {"beforeDevCommand": "yarn dev", "beforeBuildCommand": "yarn build", "devPath": "http://localhost:1420", "distDir": "../dist"}, "package": {"productName": "huditor", "version": "0.0.0"}, "tauri": {"allowlist": {"all": true}, "bundle": {"active": true, "category": "DeveloperTool", "copyright": "", "deb": {"depends": []}, "externalBin": [], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "identifier": "com.huditor.dev", "longDescription": "", "macOS": {"entitlements": null, "exceptionDomain": "", "frameworks": [], "providerShortName": null, "signingIdentity": null}, "resources": [], "shortDescription": "", "targets": "all", "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}}, "security": {"csp": null}, "updater": {"active": false}, "windows": [{"fullscreen": false, "height": 600, "resizable": true, "title": "huditor", "width": 1024, "decorations": false}]}}