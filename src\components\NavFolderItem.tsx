import { nanoid } from "nanoid";
import { useState, useRef } from "react";
import { readDirectory, writeFile } from "../helpers/filesys";
import { saveFileObject } from "../stores/file";
import { IFile } from "../types";
import NavFiles from "./NavFiles";
import { useDrag, useDrop } from "react-dnd";
import { invoke } from "@tauri-apps/api/tauri";

interface Props {
  file: IFile;
  active: boolean;
}

export default function NavFolderItem({ file, active }: Props) {
  const [files, setFiles] = useState<IFile[]>([]);
  const [unfold, setUnfold] = useState(false);
  const [loaded, setLoaded] = useState(false);
  const [newFile, setNewFile] = useState(false);
  const [filename, setFilename] = useState('');
  const [contextMenu, setContextMenu] = useState<{ x: number; y: number } | null>(null);
  const [isRenaming, setIsRenaming] = useState(false);
  const [newName, setNewName] = useState(file.name);

  const onShow = async (ev: React.MouseEvent<HTMLSpanElement>) => {
    ev.stopPropagation();
    if (loaded) {
      setUnfold(!unfold);
      return;
    }
    const entries = await readDirectory(file.path + '/');
    setLoaded(true);
    setFiles(entries);
    setUnfold(!unfold);
  };

  const onEnter = (key: string) => {
    if (key === 'Escape') {
      setNewFile(false);
      setFilename('');
      return;
    }
    if (key !== 'Enter') return;
    const filePath = `${file.path}/${filename}`;
    writeFile(filePath, '').then(() => {
      const id = nanoid();
      const newFileObj: IFile = { id, name: filename, path: filePath, kind: 'file' };
      saveFileObject(id, newFileObj);
      setFiles(prevEntries => [newFileObj, ...prevEntries]);
      setNewFile(false);
      setFilename('');
    });
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    setContextMenu({ x: e.clientX, y: e.clientY });
  };

  const handleRename = () => {
    setContextMenu(null);
    setIsRenaming(true);
  };

  const handleDelete = async () => {
    setContextMenu(null);
    await invoke("remove_folder_command", { path: file.path });
    // Refresh parent files (assuming a way to notify parent)
  };

  const handleRenameSubmit = async () => {
    const newPath = file.path.replace(file.name, newName);
    await invoke("move_path_command", { oldPath: file.path, newPath });
    setIsRenaming(false);
    // Refresh files
    const entries = await readDirectory(file.path + '/');
    setFiles(entries);
  };

  const [{ isOver }, drop] = useDrop<{ id: string; path: string }, void, { isOver: boolean }>({
    accept: "file",
    drop: (item) => {
      (async () => {
        if (item.id !== file.id) {
          const newPath = `${file.path}/${item.path.split('/').pop()}`;
          await invoke("move_path_command", { oldPath: item.path, newPath });
          const entries = await readDirectory(file.path + '/');
          setFiles(entries);
        }
      })();
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
    }),
  });

  const [{ isDragging }, drag] = useDrag<{ id: string; path: string }, unknown, { isDragging: boolean }>({
    type: "file",
    item: { id: file.id, path: file.path },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  });

  return (
    <div className="soure-item">
      <div
        className={`source-folder ${active ? 'bg-gray-200' : ''} flex items-center gap-2 px-2 py-0.5 text-gray-500 hover:text-gray-400 cursor-pointer`}
        ref={(node) => drag(drop(node))}
        onContextMenu={handleContextMenu}
      >
        <i className="ri-folder-fill text-yellow-500"></i>
        <div className="source-header flex items-center justify-between w-full group">
          {isRenaming ? (
            <input
              type="text"
              value={newName}
              onChange={(e) => setNewName(e.target.value)}
              onKeyUp={(e) => {
                if (e.key === 'Enter') handleRenameSubmit();
                if (e.key === 'Escape') setIsRenaming(false);
              }}
              onBlur={() => setIsRenaming(false)}
              autoFocus
              className="inp"
            />
          ) : (
            <span onClick={onShow}>{file.name}</span>
          )}
          <i onClick={() => setNewFile(true)} className="ri-add-line invisible group-hover:visible"></i>
        </div>
      </div>
      {contextMenu && (
        <div
          style={{ position: 'absolute', left: contextMenu.x, top: contextMenu.y, background: 'rgba(20, 22, 28, 0.75)', border: '1px solid rgba(255, 255, 255, 0.1)', borderRadius: '8px', padding: '8px' }}
        >
          <button onClick={handleRename}>Rename</button>
          <button onClick={handleDelete}>Delete</button>
        </div>
      )}
      {newFile ? (
        <div className="mx-4 flex items-center gap-0.5 p-2">
          <i className="ri-file-edit-line text-gray-300"></i>
          <input
            type="text"
            value={filename}
            onChange={(ev) => setFilename(ev.target.value)}
            onKeyUp={(ev) => onEnter(ev.key)}
            className="inp"
          />
        </div>
      ) : null}
      <NavFiles visible={unfold} files={files} />
    </div>
  );
}
