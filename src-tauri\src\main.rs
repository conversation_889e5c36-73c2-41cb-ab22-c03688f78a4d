#![cfg_attr(
    all(not(debug_assertions), target_os = "windows"),
    windows_subsystem = "windows"
)]

mod fc;
mod knowledge_base;
mod ai_service;
mod agent_manager;
mod websocket;

use knowledge_base::KnowledgeBase;
use ai_service::{AIService, ChatMessage, AIResponse};
use agent_manager::{AgentManager, AgentType, AgentStatus, Agent};
use std::sync::Mutex;
use rusqlite::Result;
use tauri::{CustomMenuItem, Menu, MenuItem, Submenu, State};
use tauri::Manager;

// Existing commands
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
fn open_folder(folder_path: &str) -> String {
    let files = fc::read_directory(folder_path);
    files
}

#[tauri::command]
fn get_file_content(file_path: &str) -> String {
    let content = fc::read_file(file_path);
    content
}

#[tauri::command]
fn write_file(file_path: &str, content: &str) -> String {
    fc::write_file(file_path, content);
    String::from("OK")
}

#[tauri::command]
fn insert_snippet(state: State<Mutex<KnowledgeBase>>, code: &str, language: &str, description: &str, tags: &str) -> Result<(), String> {
    let kb = state.lock().map_err(|e| e.to_string())?;
    kb.insert_snippet(code, language, description, tags).map_err(|e| e.to_string())
}

#[tauri::command]
fn query_snippets(state: State<Mutex<KnowledgeBase>>, query: &str) -> Result<Vec<(String, String, String, String)>, String> {
    let kb = state.lock().map_err(|e| e.to_string())?;
    kb.query_snippets(query).map_err(|e| e.to_string())
}

#[tauri::command]
fn delete_snippet(state: State<Mutex<KnowledgeBase>>, id: i64) -> Result<(), String> {
    let kb = state.lock().map_err(|e| e.to_string())?;
    kb.delete_snippet(id).map_err(|e| e.to_string())
}

#[tauri::command]
fn move_path_command(old_path: &str, new_path: &str) -> Result<(), String> {
    fc::move_path(old_path, new_path).map_err(|e| e.to_string())
}

#[tauri::command]
fn remove_file_command(path: &str) -> Result<(), String> {
    fc::remove_file(path).map_err(|e| e.to_string())
}

#[tauri::command]
fn remove_folder_command(path: &str) -> Result<(), String> {
    fc::remove_folder(path).map_err(|e| e.to_string())
}

// --- 1.2: New production-grade backend features ---

// AI chat (OpenAI)
#[tauri::command]
async fn ai_chat(
    state: State<'_, Mutex<AIService>>,
    messages: Vec<ChatMessage>,
    provider: String,
) -> Result<AIResponse, String> {
    let service = state.lock().map_err(|e| e.to_string())?;
    service.chat_completion(messages, &provider).await.map_err(|e| e.to_string())
}

// Agent manager CRUD
#[tauri::command]
fn create_agent(state: State<'_, Mutex<AgentManager>>, agent_type: AgentType) -> Result<String, String> {
    let manager = state.lock().map_err(|e| e.to_string())?;
    Ok(manager.create_agent(agent_type))
}

#[tauri::command]
fn get_agent(state: State<'_, Mutex<AgentManager>>, agent_id: String) -> Result<Agent, String> {
    let manager = state.lock().map_err(|e| e.to_string())?;
    manager.get_agent(&agent_id).ok_or("Agent not found".to_string())
}

#[tauri::command]
fn update_agent_status(state: State<'_, Mutex<AgentManager>>, agent_id: String, status: AgentStatus) -> Result<(), String> {
    let manager = state.lock().map_err(|e| e.to_string())?;
    if manager.update_agent_status(&agent_id, status) {
        Ok(())
    } else {
        Err("Agent not found".to_string())
    }
}

#[tauri::command]
fn list_agents(state: State<'_, Mutex<AgentManager>>) -> Result<Vec<Agent>, String> {
    let manager = state.lock().map_err(|e| e.to_string())?;
    Ok(manager.list_agents())
}

// Knowledge base: project indexing, context, agent sessions
#[tauri::command]
fn index_project(state: State<Mutex<KnowledgeBase>>, project_path: String) -> Result<(), String> {
    let kb = state.lock().map_err(|e| e.to_string())?;
    kb.index_project(&project_path).map_err(|e| e.to_string())
}

#[tauri::command]
fn store_context(state: State<Mutex<KnowledgeBase>>, file_path: String, context: String) -> Result<(), String> {
    let kb = state.lock().map_err(|e| e.to_string())?;
    kb.store_context(&file_path, &context).map_err(|e| e.to_string())
}

#[tauri::command]
fn query_context(state: State<Mutex<KnowledgeBase>>, query: String) -> Result<Vec<String>, String> {
    let kb = state.lock().map_err(|e| e.to_string())?;
    kb.query_context(&query).map_err(|e| e.to_string())
}

#[tauri::command]
fn store_agent_session(state: State<Mutex<KnowledgeBase>>, agent_id: String, session_data: String) -> Result<(), String> {
    let kb = state.lock().map_err(|e| e.to_string())?;
    kb.store_agent_session(&agent_id, &session_data).map_err(|e| e.to_string())
}

// Project-wide file search
#[tauri::command]
fn project_file_list(root_path: String) -> Vec<String> {
    let indexer = fc::ProjectIndexer::new();
    indexer.index_project(&root_path)
}

// File watching (prints events to stdout)
#[tauri::command]
fn watch_path(path: String) {
    let watcher = fc::FileWatcher::new();
    watcher.watch(&path);
}

#[tauri::command]
fn watch_path_with_events(path: String, app: tauri::AppHandle) {
    let watcher = fc::FileWatcher::new();
    watcher.watch_and_emit(&path, app);
}

#[tauri::command]
fn search_files_by_name(root_path: String, pattern: String) -> Vec<String> {
    fc::search_files_by_name(&root_path, &pattern)
}

#[tauri::command]
fn search_files_by_content(root_path: String, pattern: String) -> Vec<String> {
    fc::search_files_by_content(&root_path, &pattern)
}

#[tauri::command]
fn batch_delete(paths: Vec<String>) -> Vec<Result<(), String>> {
    fc::batch_delete(paths)
}

#[tauri::command]
fn batch_move(ops: Vec<(String, String)>) -> Vec<Result<(), String>> {
    fc::batch_move(ops)
}

#[tauri::command]
fn batch_copy(ops: Vec<(String, String)>) -> Vec<Result<(), String>> {
    fc::batch_copy(ops)
}

fn main() {
    tauri::Builder::default()
        .setup(|app| {
            let config = app.config();
            let kb = KnowledgeBase::new(&config)?;
            app.manage(Mutex::new(kb));
            app.manage(Mutex::new(AIService::new()));
            app.manage(Mutex::new(AgentManager::new()));
            // Initialize logging
            #[cfg(debug_assertions)]
            {
                env_logger::init();
            }
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            greet, open_folder, get_file_content, write_file,
            insert_snippet, query_snippets, delete_snippet,
            move_path_command, remove_file_command, remove_folder_command,
            ai_chat, create_agent, get_agent, update_agent_status, list_agents,
            index_project, store_context, query_context, store_agent_session,
            project_file_list, watch_path, watch_path_with_events,
            search_files_by_name, search_files_by_content,
            batch_delete, batch_move, batch_copy
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
