import { useEffect, useRef } from "react";
import Editor from "@monaco-editor/react";
import { getFileObject } from "../stores/file";
import { readFile, writeFile } from "../helpers/filesys";
import { editor } from "monaco-editor";

function CodeEditor({ id, active }: { id: string; active: boolean }) {
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);
  const visible = active ? '' : 'hidden';

  const handleEditorDidMount = (editorInstance: editor.IStandaloneCodeEditor) => {
    editorRef.current = editorInstance;
  };

  useEffect(() => {
    if (active && editorRef.current) {
      const file = getFileObject(id);
      readFile(file.path).then(content => {
        editorRef.current?.setValue(content);
      });
    }
  }, [active, id]);

  const onSave = () => {
    if (!editorRef.current) return;
    const content = editorRef.current.getValue();
    const file = getFileObject(id);
    writeFile(file.path, content);
  };

  return (
    <div className={`w-full h-full ${visible}`} onKeyDown={(ev) => {
      if (ev.ctrlKey && ev.key === 's') {
        ev.preventDefault();
        onSave();
      }
    }}>
      <Editor
        height="100%"
        defaultLanguage="javascript"  // Dynamically set based on file extension
        theme="cyberpunk-theme"
        onMount={handleEditorDidMount}
        options={{
          minimap: { enabled: true },
          fontFamily: 'JetBrains Mono',
          fontSize: 14,
          lineNumbers: 'on',
          folding: true,
          scrollBeyondLastLine: false,
          renderLineHighlight: 'all',
          wordWrap: 'wordWrapColumn',
          wordWrapColumn: 80,
        }}
      />
    </div>
  );
}

// Define custom theme before rendering
editor.defineTheme('cyberpunk-theme', {
  base: 'vs-dark',
  inherit: true,
  rules: [
    { token: 'comment', foreground: '#6272a4' },
    { token: 'keyword', foreground: '#b026ff' },  // Neon purple
    { token: 'string', foreground: '#ff2d95' },   // Neon pink
    { token: 'number', foreground: '#0099ff' },   // Neon blue
    // Add more rules for neon syntax
  ],
  colors: {
    'editor.background': '#0f1117',
    'editor.foreground': '#e0e0e0',
    'editor.lineHighlightBackground': '#1a1c25',
    'editorCursor.foreground': '#b026ff',
    'editor.selectionBackground': '#3e4451',
  }
});

export default CodeEditor;
