import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { invoke } from '@tauri-apps/api/tauri';

interface FileStore {
  openFiles: string[];
  activeFile: string | null;
  projectRoot: string | null;

  openFile: (filePath: string) => Promise<void>;
  closeFile: (filePath: string) => void;
  setActiveFile: (filePath: string) => void;
  setProjectRoot: (path: string) => void;

  readFile: (path: string) => Promise<string>;
  writeFile: (path: string, content: string) => Promise<void>;
  openFolder: (path: string) => Promise<any[]>;
}

export const useFileStore = create<FileStore>()(
  persist(
    (set, get) => ({
      openFiles: [],
      activeFile: null,
      projectRoot: null,

      openFile: async (filePath) => {
        try {
          await get().readFile(filePath);
          set((state) => ({
            openFiles: state.openFiles.includes(filePath)
              ? state.openFiles
              : [...state.openFiles, filePath],
            activeFile: filePath
          }));
        } catch (error) {
          console.error('Failed to open file:', error);
        }
      },

      closeFile: (filePath) => set((state) => ({
        openFiles: state.openFiles.filter(f => f !== filePath),
        activeFile: state.activeFile === filePath
          ? state.openFiles[0] || null
          : state.activeFile
      })),

      setActiveFile: (filePath) => set({ activeFile: filePath }),
      setProjectRoot: (path) => set({ projectRoot: path }),

      readFile: async (path: string) => {
        return await invoke<string>('get_file_content', { filePath: path });
      },

      writeFile: async (path: string, content: string) => {
        await invoke('write_file', { filePath: path, content });
      },

      openFolder: async (path: string) => {
        const result = await invoke('open_folder', { folderPath: path });
        return JSON.parse(result as string);
      }
    }),
    { name: 'file-store' }
  )
);
