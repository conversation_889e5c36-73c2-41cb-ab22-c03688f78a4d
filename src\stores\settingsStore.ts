import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface AIProviderConfig {
  openaiKey?: string;
  anthropicKey?: string;
  [key: string]: any;
}

interface SettingsStore {
  preferences: Record<string, any>;
  aiProviders: AIProviderConfig;

  setPreference: (key: string, value: any) => void;
  setAIProviderKey: (provider: string, key: string) => void;
}

export const useSettingsStore = create<SettingsStore>()(
  persist(
    (set, get) => ({
      preferences: {},
      aiProviders: {},

      setPreference: (key, value) => set((state) => ({
        preferences: { ...state.preferences, [key]: value }
      })),
      setAIProviderKey: (provider, key) => set((state) => ({
        aiProviders: { ...state.aiProviders, [provider]: key }
      })),
    }),
    { name: 'settings-store' }
  )
);
