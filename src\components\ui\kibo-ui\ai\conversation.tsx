'use client';

import { Button } from '@/components/ui/button';
import { ArrowDownIcon } from 'lucide-react';
import type { ComponentProps } from 'react';
import { useCallback } from 'react';
import { StickToBottom, useStickToBottomContext } from 'use-stick-to-bottom';
import { cn } from '@/lib/utils';

// TypeScript workaround: StickToBottom returns ReactNode, not JSX.Element, which causes TS errors in JSX. We cast to any for compatibility.
const StickToBottomAny = StickToBottom as unknown as React.ComponentType<any>;
const StickToBottomContentAny = (StickToBottom as { Content: React.ComponentType<any> }).Content;

export type AIConversationProps = ComponentProps<typeof StickToBottomAny>;

export const AIConversation: React.FC<AIConversationProps> = (props) => (
  <StickToBottomAny {...props} />
);

export const AIConversationContent = (props: any) => (
  <StickToBottomContentAny {...props} />
);

export const AIConversationScrollButton = () => {
  const { isAtBottom, scrollToBottom } = useStickToBottomContext();

  const handleScrollToBottom = useCallback(() => {
    scrollToBottom();
  }, [scrollToBottom]);

  return (
    !isAtBottom && (
      <Button
        className="absolute bottom-4 left-[50%] translate-x-[-50%] rounded-full"
        onClick={handleScrollToBottom}
        size="icon"
        type="button"
        variant="outline"
      >
        <ArrowDownIcon className="size-4" />
      </Button>
    )
  );
};
