[package]
name = "blazecoder"
version = "0.1.0"
description = "BlazeCoder: AI-Powered Agentic Coder IDE"
authors = ["you"]
license = ""
repository = ""
edition = "2021"
rust-version = "1.57"

[build-dependencies]
tauri-build = { version = "1.1", features = [] }

[dependencies]
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tauri = { version = "1.1", features = ["api-all"] }
rusqlite = { version = "0.30", features = ["bundled"] }
reqwest = { version = "0.11", features = ["json", "blocking", "rustls-tls"] }
tokio = { version = "1.0", features = ["full"] }
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
tokio-tungstenite = "0.20"
futures-util = "0.3"
notify = "6.0"
walkdir = "2.3"
md5 = "0.7"

[features]
# by default <PERSON><PERSON> runs in production mode
# when `tauri dev` runs it is executed with `cargo run --no-default-features` if `devPath` is an URL
default = [ "custom-protocol" ]
# this feature is used used for production builds where `devPath` points to the filesystem
# DO NOT remove this
custom-protocol = [ "tauri/custom-protocol" ]
