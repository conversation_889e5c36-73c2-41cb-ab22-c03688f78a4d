use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;
use notify::{Watcher, RecursiveM<PERSON>, watcher, DebouncedEvent};
use std::sync::mpsc::channel;
use std::time::Duration;
use walkdir::WalkDir;
use tauri::AppHandle;
use log::{info, error};

// #[warn(dead_code)]
#[derive(Serialize, Deserialize, Debug)]
pub struct FileInfo {
    name: String,
    kind: String,
    path: String,
}

#[derive(Serialize, Deserialize)]
pub struct Post {
    title: String,
    created: String,
    link: String,
    description: String,
    content: String,
    author: String,
}

pub fn read_directory(dir_path: &str) -> String {
    let new_path = Path::new(dir_path);
    println!("new path {:?}", new_path);
    let paths = fs::read_dir(new_path).unwrap();

    let mut files: Vec<FileInfo> = Vec::new();

    for path in paths {
        let path_unwrap = path.unwrap();
        let meta = path_unwrap.metadata();
        let meta_unwrap = meta.unwrap();

        let mut kind = String::from("file");

        if meta_unwrap.is_dir() {
            kind = String::from("directory");
        }

        let filename = match path_unwrap.file_name().into_string() {
            Ok(str) => str,
            Err(_error) => String::from("ERROR"),
        };

        let file_path = dir_path.to_owned() + &filename;

        let new_file_info = FileInfo {
            name: filename,
            kind,
            path: file_path,
        };

        files.push(new_file_info);
    }

    let files_str = match serde_json::to_string(&files) {
        Ok(str) => str,
        Err(error) => panic!("Problem opening the file: {:?}", error),
    };

    // println!("file {:?}", files_str);

    files_str
}

pub fn read_file(path: &str) -> String {
    let contents = fs::read_to_string(path).expect("ERROR");
    contents
}

// update file and create new file
pub fn write_file(path: &str, content: &str) -> String {
    let file_path = Path::new(path);
    let result = match fs::write(file_path, content) {
        Ok(()) => String::from("OK"),
        Err(_err) => String::from("ERROR")
    };

    result
}

pub fn create_directory(path: &str) -> std::result::Result<(), std::io::Error>{
    let dir_path = Path::new(path);
    fs::create_dir(dir_path)?;
    Ok(())
}

pub fn remove_file(path: &str) -> std::result::Result<(), std::io::Error> {
    let file_path = Path::new(path);
    fs::remove_file(file_path)?;
    Ok(())
}

pub fn remove_folder(path: &str) -> std::result::Result<(), std::io::Error>{ 
    let folder_path = Path::new(path);
    fs::remove_dir_all(folder_path)?;
    Ok(())
}

pub fn move_path(old_path: &str, new_path: &str) -> std::result::Result<(), std::io::Error> {
    let old = Path::new(old_path);
    let new = Path::new(new_path);
    fs::rename(old, new)?;
    Ok(())
}

// File watching logic using notify crate
// TODO: Add dependency: notify

#[allow(dead_code)]
pub struct FileWatcher {
    // No fields needed for this simple implementation
}

#[allow(dead_code)]
impl FileWatcher {
    pub fn new() -> Self {
        Self {}
    }

    pub fn watch_and_emit(&self, path: &str, app_handle: AppHandle) {
        let (tx, rx) = channel();
        let mut watcher = match watcher(tx, Duration::from_secs(2)) {
            Ok(w) => w,
            Err(e) => {
                error!("Failed to create watcher: {}", e);
                return;
            }
        };
        if let Err(e) = watcher.watch(path, RecursiveMode::Recursive) {
            error!("Failed to watch path {}: {}", path, e);
            return;
        }
        info!("Watching for changes in {}", path);
        std::thread::spawn(move || {
            for event in rx {
                match &event {
                    DebouncedEvent::Create(p) => info!("Created: {:?}", p),
                    DebouncedEvent::Write(p) => info!("Modified: {:?}", p),
                    DebouncedEvent::Remove(p) => info!("Removed: {:?}", p),
                    DebouncedEvent::Rename(f, t) => info!("Renamed: {:?} -> {:?}", f, t),
                    DebouncedEvent::Error(e, p) => error!("Error: {:?} on {:?}", e, p),
                    _ => (),
                }
                // Emit event to frontend
                let _ = app_handle.emit_all("file_change", &format!("{:?}", event));
            }
        });
    }
}

// Advanced project-wide search and indexing using walkdir
// TODO: Add dependency: walkdir

#[allow(dead_code)]
pub struct ProjectIndexer;

#[allow(dead_code)]
impl ProjectIndexer {
    pub fn new() -> Self {
        Self {}
    }

    pub fn index_project(&self, root_path: &str) -> Vec<String> {
        WalkDir::new(root_path)
            .into_iter()
            .filter_map(|e| e.ok())
            .filter(|e| e.path().is_file())
            .map(|e| e.path().to_string_lossy().to_string())
            .collect()
    }
}

#[allow(dead_code)]
pub fn search_files_by_name(root_path: &str, pattern: &str) -> Vec<String> {
    WalkDir::new(root_path)
        .into_iter()
        .filter_map(|e| e.ok())
        .filter(|e| e.path().is_file())
        .filter(|e| e.file_name().to_string_lossy().contains(pattern))
        .map(|e| e.path().to_string_lossy().to_string())
        .collect()
}

#[allow(dead_code)]
pub fn search_files_by_content(root_path: &str, pattern: &str) -> Vec<String> {
    WalkDir::new(root_path)
        .into_iter()
        .filter_map(|e| e.ok())
        .filter(|e| e.path().is_file())
        .filter(|e| {
            if let Ok(content) = std::fs::read_to_string(e.path()) {
                content.contains(pattern)
            } else {
                false
            }
        })
        .map(|e| e.path().to_string_lossy().to_string())
        .collect()
}

#[allow(dead_code)]
pub fn batch_delete(paths: Vec<String>) -> Vec<Result<(), String>> {
    paths.into_iter().map(|p| {
        fs::remove_file(&p).map_err(|e| format!("Failed to delete {}: {}", p, e))
    }).collect()
}

#[allow(dead_code)]
pub fn batch_move(ops: Vec<(String, String)>) -> Vec<Result<(), String>> {
    ops.into_iter().map(|(from, to)| {
        fs::rename(&from, &to).map_err(|e| format!("Failed to move {} to {}: {}", from, to, e))
    }).collect()
}

#[allow(dead_code)]
pub fn batch_copy(ops: Vec<(String, String)>) -> Vec<Result<(), String>> {
    ops.into_iter().map(|(from, to)| {
        fs::copy(&from, &to).map(|_| ()).map_err(|e| format!("Failed to copy {} to {}: {}", from, to, e))
    }).collect()
}
