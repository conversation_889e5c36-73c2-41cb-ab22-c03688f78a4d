import { MouseEvent } from "react";
import { useSource } from "../context/SourceContext";
import { IFile } from "../types";
import FileIcon from "./FileIcon";
import NavFolderItem from "./NavFolderItem";
import FileItem from "./FileItem"; // Ensure import is present

interface Props {
  files: IFile[];
  visible: boolean;
}

export default function NavFiles({ files, visible }: Props) {
  const { setSelect, selected, addOpenedFile } = useSource();

  const onShow = (ev: MouseEvent<HTMLDivElement>, file: IFile) => {
    ev.stopPropagation();
    if (file.kind === 'file') {
      setSelect(file.id);
      addOpenedFile(file.id);
    }
  };

  return (
    <div className={`source-codes ${visible ? '' : 'hidden'}`}>
      {files.map(file => {
        const isSelected = file.id === selected;
        if (file.kind === 'directory') {
          return <NavFolderItem active={isSelected} key={file.id} file={file} />;
        }
        return <FileItem key={file.id} file={file} onShow={onShow} />;
      })}
    </div>
  );
}
