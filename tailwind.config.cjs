module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {
      colors: {
        background: '#0f1117',
        primary: 'rgba(15, 17, 23, 0.85)', // Added primary color for bg-primary usage
        surface: {
          primary: 'rgba(15, 17, 23, 0.85)',
          secondary: 'rgba(20, 22, 28, 0.75)',
          tertiary: 'rgba(25, 27, 33, 0.65)'
        },
        neon: {
          purple: {
            DEFAULT: '#b026ff',
            glow: 'rgba(176, 38, 255, 0.15)',
            hover: 'rgba(176, 38, 255, 0.25)',
            active: 'rgba(176, 38, 255, 0.35)'
          },
          blue: {
            DEFAULT: '#0099ff',
            glow: 'rgba(0, 153, 255, 0.15)',
            hover: 'rgba(0, 153, 255, 0.25)',
            active: 'rgba(0, 153, 255, 0.35)'
          },
          pink: {
            DEFAULT: '#ff2d95',
            glow: 'rgba(255, 45, 149, 0.15)',
            hover: 'rgba(255, 45, 149, 0.25)',
            active: 'rgba(255, 45, 149, 0.35)'
          }
        },
        border: {
          DEFAULT: 'rgba(255, 255, 255, 0.1)',
          hover: 'rgba(255, 255, 255, 0.2)',
          active: 'rgba(255, 255, 255, 0.3)'
        }
      },
      fontFamily: {
        mono: ['JetBrains Mono', 'monospace'],
        sans: ['Inter', 'sans-serif']
      },
      boxShadow: {
        'glass': '0 4px 32px rgba(0, 0, 0, 0.15)',
        'neon-purple': '0 0 20px rgba(176, 38, 255, 0.3)',
        'neon-blue': '0 0 20px rgba(0, 153, 255, 0.3)',
        'neon-pink': '0 0 20px rgba(255, 45, 149, 0.3)'
      },
      backdropBlur: {
        'glass': '8px'
      },
      animation: {
        'glow-purple': 'glow-purple 2s ease-in-out infinite',
        'glow-blue': 'glow-blue 2s ease-in-out infinite',
        'glow-pink': 'glow-pink 2s ease-in-out infinite',
        'float': 'float 3s ease-in-out infinite'
      },
      keyframes: {
        'glow-purple': {
          '0%, 100%': { boxShadow: '0 0 20px rgba(176, 38, 255, 0.3)' },
          '50%': { boxShadow: '0 0 30px rgba(176, 38, 255, 0.6)' }
        },
        'glow-blue': {
          '0%, 100%': { boxShadow: '0 0 20px rgba(0, 153, 255, 0.3)' },
          '50%': { boxShadow: '0 0 30px rgba(0, 153, 255, 0.6)' }
        },
        'glow-pink': {
          '0%, 100%': { boxShadow: '0 0 20px rgba(255, 45, 149, 0.3)' },
          '50%': { boxShadow: '0 0 30px rgba(255, 45, 149, 0.6)' }
        },
        'float': {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-10px)' }
        }
      }
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms')({ strategy: 'class' })
  ],
}
