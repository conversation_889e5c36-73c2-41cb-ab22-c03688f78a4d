import { Badge } from '@/components/ui/badge';
import { Tooltip } from '@/components/ui/tooltip';
import { AgentStatus as StatusType } from '@/stores/agentStore';

const statusColor: Record<StatusType, string> = {
  Idle: 'bg-gray-400',
  Thinking: 'bg-blue-500',
  Working: 'bg-green-500',
  Error: 'bg-red-500',
};

export function AgentStatus({ status }: { status: StatusType }) {
  return (
    <Tooltip content={status}>
      <Badge className={statusColor[status] + ' text-white'}>{status}</Badge>
    </Tooltip>
  );
} 