import { useState } from 'react';
import { useDrag } from 'react-dnd';
import { invoke } from '@tauri-apps/api/tauri';
import FileIcon from './FileIcon';
import { useSource } from '../context/SourceContext';
import { IFile } from "../types";

type Props = {
  file: IFile;
  onShow: (ev: React.MouseEvent<HTMLDivElement, MouseEvent>, file: IFile) => void;
};

export default function FileItem({ file, onShow }: Props) {
  const { setSelect, selected, addOpenedFile } = useSource();
  const isSelected = file.id === selected;

  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'file',
    item: { id: file.id, path: file.path },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  }));

  const [contextMenu, setContextMenu] = useState<{ x: number; y: number } | null>(null);
  const [isRenaming, setIsRenaming] = useState(false);
  const [newName, setNewName] = useState(file.name);

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setContextMenu({ x: e.clientX, y: e.clientY });
  };

  const handleRename = () => {
    setContextMenu(null);
    setIsRenaming(true);
  };

  const handleDelete = async () => {
    setContextMenu(null);
    await invoke('remove_file', { path: file.path });
    // Parent will need to refresh files
  };

  const handleRenameSubmit = async () => {
    const newPath = file.path.replace(file.name, newName);
    await invoke('move_path_command', { oldPath: file.path, newPath });
    setIsRenaming(false);
    // Parent refresh
  };

  return (
    <div
      ref={drag}
      onClick={(ev) => onShow(ev, file)}
      onContextMenu={handleContextMenu}
      className={`soure-item ${isSelected ? 'source-item-active' : ''} flex items-center gap-2 px-2 py-0.5 text-gray-500 hover:text-gray-400 cursor-pointer ${isDragging ? 'opacity-50' : ''}`}
    >
      <FileIcon name={file.name} />
      {isRenaming ? (
        <input
          type="text"
          value={newName}
          onChange={(e) => setNewName(e.target.value)}
          onKeyUp={(e) => {
            if (e.key === 'Enter') handleRenameSubmit();
            if (e.key === 'Escape') setIsRenaming(false);
          }}
          onBlur={() => setIsRenaming(false)}
          autoFocus
          className="inp"
        />
      ) : (
        <span>{file.name}</span>
      )}
    </div>
  );
}