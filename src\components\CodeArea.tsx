import { IFile } from "../types"
import { useSource } from "../context/SourceContext"
import { getFileObject } from "../stores/file"
import FileIcon from "./FileIcon"
import PreviewImage from "./PreviewImage"
import CodeEditor from "./CodeEditor"
import { DndProvider, useDrag, useDrop } from "react-dnd"
import { HTML5Backend } from "react-dnd-html5-backend"
import { motion } from "framer-motion"
import { useState } from "react"

const ItemType = 'TAB'

interface TabItemProps {
  id: string;
  index: number;
  moveTab: (from: number, to: number) => void;
  name: string;
  isSelected: boolean;
  onSelect: (id: string) => void;
  onClose: (ev: React.MouseEvent<HTMLElement>, id: string) => void;
}

function TabItem({ id, index, moveTab, name, isSelected, onSelect, onClose }: TabItemProps) {
  const [{ isDragging }, drag] = useDrag<{ id: string; index: number }, unknown, { isDragging: boolean }>({
    type: ItemType,
    item: { id, index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop<{ id: string; index: number }>({
    accept: ItemType,
    hover: (item) => {
      if (item.index !== index) {
        moveTab(item.index, index);
        item.index = index;
      }
    },
  });

  return (
    <motion.div
      ref={(node) => drag(drop(node))}
      className={`tab-item shrink-0 px-3 py-1.5 text-gray-500 cursor-pointer flex items-center gap-2 ${isSelected ? 'bg-surface-secondary text-neon-purple border-b-2 border-neon-purple' : ''} ${isDragging ? 'opacity-50' : ''}`}
      onClick={() => onSelect(id)}
      whileHover={{ scale: 1.05 }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <FileIcon name={name} size="sm" />
      <span>{name}</span>
      <motion.i
        onClick={(ev: React.MouseEvent<HTMLElement>) => onClose(ev, id)}
        className="ri-close-line hover:text-neon-pink"
        whileHover={{ rotate: 90, scale: 1.2 }}
      />
    </motion.div>
  )
}

export default function CodeArea() {
  const { opened, selected, setSelect, delOpenedFile, setOpenedFiles } = useSource()
  const [previewTab, setPreviewTab] = useState(null)

  const moveTab = (fromIndex: number, toIndex: number) => {
    const newOpened = [...opened]
    const [movedTab] = newOpened.splice(fromIndex, 1)
    newOpened.splice(toIndex, 0, movedTab)
    setOpenedFiles(newOpened)
  }

  const onSelectItem = (id: string) => {
    setSelect(id)
  }

  const close = (ev: React.MouseEvent<HTMLElement>, id: string) => {
    ev.stopPropagation()
    delOpenedFile(id)
  }

  const isImage = (name: string) => {
    return ['.png', '.gif', '.jpeg', '.jpg', '.bmp'].some(ext => name.lastIndexOf(ext) !== -1) 
  }

  return <div id="code-area" className="w-full h-full">
    <DndProvider backend={HTML5Backend}>
      <div className="code-tab-items flex items-center border-b border-surface-tertiary overflow-x-auto bg-surface-primary">
        {opened.map((item, index) => {
          const file = getFileObject(item) as IFile
          const active = selected === item
          return (
            <TabItem
              key={item}
              id={item}
              index={index}
              moveTab={moveTab}
              name={file.name}
              isSelected={active}
              onSelect={onSelectItem}
              onClose={close}
            />
          )
        })}
      </div>
    </DndProvider>
    <div className="code-contents relative">
      {opened.map(item => {
        const file = getFileObject(item) as IFile
        if (isImage(file.name)) {
          return <PreviewImage key={item} path={file.path} active={item === selected} />
        }
        return <CodeEditor key={item} id={item} active={item===selected} />
      })}
    </div>
  </div>
}
